package com.socialplay.gpark.data.model.choice

import com.socialplay.gpark.function.pandora.PandoraToggle

/**
 * 卡片类型 1大卡片，2小卡片，  3超大图;4词条;5双图;6四图
 * <AUTHOR>
 * @date 2021/07/05
 */
object ChoiceCardType {
    const val BIG = 1
    // 游戏
    const val SMALL = 2
    // web链接/运营位
    const val BANNER = 3
    const val STACK = 4
    const val TWO_COLUMN = 5
    const val FOUR_GRID = 6
    // 语音房
    const val ROOM = 20
    // 小屋
    const val HOME = 50
    // ugc
    const val UGC_CREATE = 51

    const val LINEAR = 1024
    const val GRID = 1025

    const val RECOMMEND = 1026
    const val RECOMMEND_HEADER = 1027

    const val RECOMMEND_VIDEO_FEED_LIST = 1028

    // 模板卡片类型
    const val TEMPLATE = 1001

    // trending强插设置：卡片名称
    const val TRENDING_NAME = "Trending"
    // trending强插设置：卡片类型
    const val TRENDING_CARD_TYPE = SMALL

    private val supportCardTypes = arrayListOf(SMALL, BANNER, ROOM, HOME, UGC_CREATE, RECOMMEND)
    fun isSupportCardType(type: Int): Boolean = supportCardTypes.contains(type)

    fun isRoomCardType(type: Int) = type == ROOM

    fun isHomeRoomCardType(type: Int) = (type == HOME && PandoraToggle.openUgcHomeEntrance)

    fun isUgcCreate(type: Int) = (type == UGC_CREATE)

    fun removeSupportCardType(type: Int) = supportCardTypes.remove(type)

    fun isTrending(cardName: String?, cardType: Int) = cardType == TRENDING_CARD_TYPE && cardName == TRENDING_NAME

    fun isHomeVideoCardType(cardType: Int) = cardType == RECOMMEND_VIDEO_FEED_LIST

    fun isSmall(type: Int) = type == SMALL
}

