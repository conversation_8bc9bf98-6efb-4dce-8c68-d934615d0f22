package com.socialplay.gpark.util

import android.content.Context
import android.graphics.*
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import com.bumptech.glide.RequestManager
import com.bumptech.glide.load.engine.bitmap_recycle.BitmapPool
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.databinding.AdapterChoiceCardItemSmallBinding
import kotlin.math.ceil
import kotlin.math.sqrt
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.screenWidth
import java.security.MessageDigest

/**
 * Created by bo.li
 * Date: 2024/2/5
 * Desc:
 */
object SmallCardViewUtil {
    fun setupCardView(
        context: Context,
        glide: RequestManager?,
        info: ChoiceGameInfo,
        binding: AdapterChoiceCardItemSmallBinding,
        hideIcon: Boolean
    ) {
        if (ScreenUtil.isPad(context)) {
            val layoutParams = binding.root.layoutParams
            layoutParams.width = ScreenUtil.dp2px(context, 144f)
            binding.root.layoutParams = layoutParams
        } else {
            val layoutParams = binding.root.layoutParams
            layoutParams.width = ScreenUtil.dp2px(context, 144f)
            binding.root.layoutParams = layoutParams
        }
        binding.apply {
            ivGameIcon.isInvisible = hideIcon
            if (!hideIcon) {
                glide?.run {
                    load(info.iconUrl).placeholder(R.drawable.placeholder_corner_12)
                        .transform(CenterCrop())
                        .into(ivGameIcon)
                        
                    load(info.iconUrl)
                        .transform(
                            GradientTransformation(ivLikeBg)
                        )
                        .into(ivLikeBg)
                }
            }
            tvGameTitle.text = info.displayName?.trim() ?: ""
            tvZan.text = UnitUtil.formatKMCount(info.localLikeCount ?: info.likeCount ?: 0)
        }
    }
}

class GradientTransformation(private val targetView: View) : BitmapTransformation() {
    override fun transform(
        pool: BitmapPool,
        toTransform: Bitmap,
        outWidth: Int,
        outHeight: Int
    ): Bitmap {
        // 先压缩图片
        val scale = 0.25f // 压缩到原来的1/4
        val scaledWidth = (toTransform.width * scale).toInt()
        val scaledHeight = (toTransform.height * scale).toInt()
        
        val scaledBitmap = pool.get(scaledWidth, scaledHeight, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(scaledBitmap)
        canvas.drawBitmap(toTransform, null, Rect(0, 0, scaledWidth, scaledHeight), null)
        
        // 对压缩后的图片进行模糊处理
        val blurredBitmap = BlurUtil.fastBlur(scaledBitmap, 25)
        pool.put(scaledBitmap)
        
        // 创建最终结果
        val result = pool.get(outWidth, outHeight, Bitmap.Config.ARGB_8888)
        val resultCanvas = Canvas(result)
        
        // 绘制模糊后的图片
        resultCanvas.drawBitmap(blurredBitmap, null, Rect(0, 0, outWidth, outHeight), null)
        pool.put(blurredBitmap)
        
        // 获取目标控件的大小
        val viewWidth = targetView.width
        val viewHeight = targetView.height
        
        // 计算渐变半径（使用控件对角线长度）
        val radius = sqrt((viewWidth * viewWidth + viewHeight * viewHeight).toFloat())
        
        // 创建径向渐变遮罩
        val paint = Paint().apply {
            shader = RadialGradient(
                0f, viewHeight.toFloat(),  // 起始点（左下角）
                radius,                    // 渐变半径
                intArrayOf(
                    Color.BLACK,       // 左下角不透明
                    Color.BLACK,       // 左下角不透明
                    Color.TRANSPARENT  // 渐变到透明
                ),
                floatArrayOf(0f, 0.2f, 0.4f),  // 渐变位置
                Shader.TileMode.CLAMP
            )
            xfermode = PorterDuffXfermode(PorterDuff.Mode.DST_IN)
        }
        
        // 应用渐变遮罩
        resultCanvas.drawRect(0f, 0f, outWidth.toFloat(), outHeight.toFloat(), paint)
        
        return result
    }

    override fun updateDiskCacheKey(messageDigest: MessageDigest) {
        messageDigest.update("GradientTransformation".toByteArray())
    }

    override fun equals(other: Any?): Boolean {
        return other is GradientTransformation && other.targetView == targetView
    }

    override fun hashCode(): Int {
        return "GradientTransformation".hashCode() + targetView.hashCode()
    }
}