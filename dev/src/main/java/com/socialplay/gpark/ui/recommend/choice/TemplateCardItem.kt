package com.socialplay.gpark.ui.recommend.choice

import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.databinding.AdapterChoiceCardItemTemplateBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.util.GradientTransformation
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.context

fun MetaModelCollector.templateCardList(
    card: ChoiceCardInfo,
    cardPosition: Int,
    spanSize: Int,
    listener: IChoiceListener
) {
    val games = card.gameList ?: return
    games.forEachIndexed { index, game ->
        add(
            TemplateCardItem(
                item = game,
                position = index,
                card = card,
                cardPosition = cardPosition,
                spanSize = spanSize,
                isTemplate = index == 0,
                listener = listener
            ).id("TemplateCardItem-$cardPosition-$index-${game.code}")
        )
    }
}

data class TemplateCardItem(
    val item: ChoiceGameInfo,
    val position: Int,
    val card: ChoiceCardInfo,
    val cardPosition: Int,
    val spanSize: Int,
    val isTemplate: Boolean,
    val listener: IChoiceListener
) : ViewBindingItemModel<AdapterChoiceCardItemTemplateBinding>(
    R.layout.adapter_choice_card_item_template,
    AdapterChoiceCardItemTemplateBinding::bind
) {
    override fun AdapterChoiceCardItemTemplateBinding.onBind() {
        if (ScreenUtil.isPad(context)) {
            val layoutParams = root.layoutParams
            layoutParams.width = ScreenUtil.dp2px(context, 212f)
            root.layoutParams = layoutParams
        } else {
            val layoutParams = root.layoutParams
            layoutParams.width = ScreenUtil.dp2px(context, 212f)
            root.layoutParams = layoutParams
        }
        // 封面
        listener.getGlideOrNull()?.run {
            load(item.iconUrl).placeholder(R.drawable.placeholder_corner_12)
                .transform(CenterCrop())
                .into(ivGameIcon)

            load(item.iconUrl)
                .transform(
                    GradientTransformation(ivLikeBg)
                )
                .into(ivLikeBg)
        }
        // 点赞
        likeView.setLikeText(UnitUtil.formatKMCount(item.localLikeCount ?: item.likeCount ?: 0))
        // 标题
        tvGameTitle.text = item.displayName
        // 星星数量
        val rating = item.difficulty?.toInt() ?: 3
        val stars = listOf(ivStar1, ivStar2, ivStar3)
        for (i in stars.indices) {
            stars[i].alpha = if (i < rating) 1f else 0.3f
        }

        // 先全部隐藏
        layoutTags.visibility = android.view.View.GONE
        btnCreate.visibility = android.view.View.GONE
        sivCreatorAvatar.visibility = android.view.View.GONE
        tvCreatorNickname.visibility = android.view.View.GONE

        if (isTemplate) {
            // 第一位 模板数据
            layoutTags.visibility = android.view.View.VISIBLE
            btnCreate.visibility = android.view.View.VISIBLE
            layoutTemplateTag.setBackgroundResource(R.drawable.bg_home_template_item)
            // 标签
            val orNull1 = item.tagList?.getOrNull(1)
            if (orNull1 == null || orNull1.isEmpty()) {
                tvTag1.visibility = android.view.View.GONE
            } else {
                tvTag1.visibility = android.view.View.VISIBLE
                tvTag1.text = orNull1
            }
            val orNull = item.tagList?.getOrNull(1)
            if (orNull == null || orNull.isEmpty()) {
                tvTag2.visibility = android.view.View.GONE
            } else {
                tvTag2.visibility = android.view.View.VISIBLE
                tvTag2.text = orNull
            }
            // 按钮
            btnCreate.setOnClickListener { listener.onMoreClick(card) }

            root.setOnClickListener {

            }
        } else {
            // 作品数据
            sivCreatorAvatar.visibility = android.view.View.VISIBLE
            tvCreatorNickname.visibility = android.view.View.VISIBLE

            layoutTemplateTag.setBackgroundResource(R.drawable.bg_home_template_child_item)
            listener.getGlideOrNull()?.run {
                load(item.avatar).placeholder(R.drawable.placeholder_round)
                    .transform(CenterCrop())
                    .into(sivCreatorAvatar)
            }
            tvCreatorNickname.text = item.nickname

            btnCreate.setOnClickListener { }
            // 普通作品可点击整个卡片
            root.setOnClickListener { listener.onItemClick(cardPosition, card, position, item, false) }
        }
    }
}