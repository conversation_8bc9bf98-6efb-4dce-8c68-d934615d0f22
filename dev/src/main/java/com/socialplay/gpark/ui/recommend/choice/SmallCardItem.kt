package com.socialplay.gpark.ui.recommend.choice

import android.text.TextUtils
import android.view.Gravity
import android.view.View
import com.airbnb.epoxy.Carousel
import com.airbnb.epoxy.VisibilityState
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.databinding.AdapterChoiceCardItemSmallBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.ui.core.views.carouselNoSnapWrapBuilder
import com.socialplay.gpark.ui.core.views.textItem
import com.socialplay.gpark.util.SmallCardViewUtil
import com.socialplay.gpark.util.extension.context
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/01
 *     desc   :
 * </pre>
 */
fun MetaModelCollector.choiceSmallCard(
    card: ChoiceCardInfo,
    cardPosition: Int,
    spanSize: Int,
    listener: IChoiceListener
) {
    val games = card.gameList
    if (games.isNullOrEmpty()) return
    textItem(
        text = card.cardName,
        textSize = 18.0f,
        fontFamily = R.font.poppins_semi_bold_600,
        textColorRes = R.color.textColorPrimary,
        isSingleLine = true,
        ellipsize = TextUtils.TruncateAt.END,
        gravity = Gravity.START or Gravity.CENTER_VERTICAL,
        paddingLeftDp = 16.0f,
        paddingTopDp = 20.0f,
        paddingRightDp = 12.0f,
        paddingBottomDp = 12.0f,
        idStr = "ChoiceSmallCardTitle-$cardPosition",
        spanSize = spanSize
    )
    carouselNoSnapWrapBuilder {
        id("ChoiceSmallCardList-$cardPosition")
        padding(Carousel.Padding.dp(16, 0, 6, 8, 0))
        hasFixedSize(true)
        initialPrefetchItemCount(3)
        onVisibilityStateChanged { _, _, visibilityState ->
            if (visibilityState == VisibilityState.VISIBLE) {
                listener.onCardShow(cardPosition, card)
            }
        }
        spanSizeOverride { _, _, _ ->
            spanSize
        }
        games.forEachIndexed { position, game ->
            add(
                ChoiceSmallCardItem(
                    game,
                    position,
                    card,
                    cardPosition,
                    spanSize,
                    listener
                ).id("ChoiceSmallCardGame-$cardPosition-$position-${game.code}")
            )
        }
    }
}

data class ChoiceSmallCardItem(
    val item: ChoiceGameInfo,
    val position: Int,
    val card: ChoiceCardInfo,
    val cardPosition: Int,
    val spanSize: Int,
    val listener: IChoiceListener
) : ViewBindingItemModel<AdapterChoiceCardItemSmallBinding>(
    R.layout.adapter_choice_card_item_small,
    AdapterChoiceCardItemSmallBinding::bind
) {

    override fun AdapterChoiceCardItemSmallBinding.onBind() {
        root.setMargin(right = dp(10))
        SmallCardViewUtil.setupCardView(context, listener.getGlideOrNull(), item, this, false)
        root.setOnAntiViolenceClickListener {
            listener.onItemClick(cardPosition, card, position, item, false)
        }
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        if (visibilityState == VisibilityState.VISIBLE) {
            listener.onItemShow(cardPosition, card, position, item, false)
        }
    }
}