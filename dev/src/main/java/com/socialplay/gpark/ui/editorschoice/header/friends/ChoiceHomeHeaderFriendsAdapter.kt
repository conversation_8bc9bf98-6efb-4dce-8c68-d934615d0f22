package com.socialplay.gpark.ui.editorschoice.header.friends

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import com.bumptech.glide.RequestManager
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceFriendInfo
import com.socialplay.gpark.data.model.choice.ChoiceFriendInfoDiff
import com.socialplay.gpark.databinding.AdapterItemFriendsBinding
import com.socialplay.gpark.ui.base.adapter.BaseDifferAdapter
import com.socialplay.gpark.ui.base.adapter.BaseVBViewHolder
import com.socialplay.gpark.util.extension.getString
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.getStringByGlobal

/**
 * 2023/8/11
 */
class ChoiceHomeHeaderFriendsAdapter(
    private val glide: RequestManager,
    private val itemWidth: Int
) :
    BaseDifferAdapter<ChoiceFriendInfo, AdapterItemFriendsBinding>(ChoiceFriendInfoDiff) {

    override fun viewBinding(parent: ViewGroup, viewType: Int): AdapterItemFriendsBinding {
        return AdapterItemFriendsBinding.inflate(LayoutInflater.from(parent.context), parent, false)
    }

    override fun convert(
        holder: BaseVBViewHolder<AdapterItemFriendsBinding>,
        item: ChoiceFriendInfo,
    ) {
        if (item.isAdd()) {
            showAddItem(holder.binding, item)
        } else {
            showUserItem(holder.binding, item)
        }
    }

    private fun showAddItem(binding: AdapterItemFriendsBinding, item: ChoiceFriendInfo) {
        // 设置Add Friend样式 - 使用黄色头像背景
        binding.ivAvatar.setImageResource(R.drawable.bg_friend_avatar_yellow)
        binding.ivAvatar.setBackground(null)
        binding.ivAddIcon.isVisible = true // 显示底部加号
        binding.llStatus.isVisible = false // 隐藏状态标签
        binding.viewAddFriendTextBg.isVisible = true // 显示文字渐变背景
        binding.tvUserName.apply {
            text = getString(R.string.add_friend)
            setTextColorByRes(R.color.Gray_1000) // 黑色文字
        }
    }

    private fun showUserItem(binding: AdapterItemFriendsBinding, item: ChoiceFriendInfo) {
        // 设置用户头像
        glide.load(item.userAvatar)
            .placeholder(item.placeholderIcon)
            .transform(CircleCrop())
            .into(binding.ivAvatar)

        binding.ivAddIcon.isVisible = false
        binding.viewAddFriendTextBg.isVisible = false // 隐藏文字渐变背景
        binding.tvUserName.apply {
            text = item.userName
            setTextColorByRes(R.color.Gray_900) // 恢复正常文字颜色
        }

        // 设置状态标签
        when {
            item.isInGame() -> {
                binding.llStatus.apply {
                    isVisible = true
                    setBackgroundResource(R.drawable.bg_status_gaming)
                }
                binding.ivStatusIconNew.setImageResource(R.drawable.ic_friend_gaming)
                binding.tvStatusText.text = getStringByGlobal(R.string.gaming_status)
                binding.ivAvatar.setBackgroundResource(R.drawable.bg_status_gaming_border)
            }

            item.isOnline() -> {
                binding.llStatus.apply {
                    isVisible = true
                    setBackgroundResource(R.drawable.bg_status_online)
                }
                binding.ivStatusIconNew.setImageResource(R.drawable.ic_friend_online)
                binding.tvStatusText.text = getStringByGlobal(R.string.online_status)
                binding.ivAvatar.setBackground(null)
            }

            else -> {
                binding.llStatus.apply {
                    isVisible = true
                    setBackgroundResource(R.drawable.bg_status_offline)
                }
                binding.ivStatusIconNew.setImageResource(R.drawable.ic_friend_offline)
                binding.tvStatusText.text = getStringByGlobal(R.string.offline_cap)
                binding.ivAvatar.setBackground(null)
            }
        }
    }
}