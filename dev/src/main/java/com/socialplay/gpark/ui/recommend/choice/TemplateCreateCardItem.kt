package com.socialplay.gpark.ui.recommend.choice

import android.text.TextUtils
import android.view.Gravity
import android.view.View
import com.airbnb.epoxy.Carousel
import com.airbnb.epoxy.VisibilityState
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.databinding.AdapterChoiceCardItemTemplateCreateBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.ui.core.views.carouselNoSnapWrapBuilder
import com.socialplay.gpark.ui.core.views.textItem
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.context
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.extension.setMargin

/**
 * 模板创建卡片
 * 用于显示模板类型的游戏卡片，包含Template标签、星级评分、Create按钮等
 */
fun MetaModelCollector.choiceTemplateCreateCard(
    card: ChoiceCardInfo,
    cardPosition: Int,
    spanSize: Int,
    listener: IChoiceListener
) {
    val games = card.gameList
    if (games.isNullOrEmpty()) return
    
    // 卡片标题
    textItem(
        text = card.cardName,
        textSize = 18.0f,
        fontFamily = R.font.poppins_semi_bold_600,
        textColorRes = R.color.textColorPrimary,
        isSingleLine = true,
        ellipsize = TextUtils.TruncateAt.END,
        gravity = Gravity.START or Gravity.CENTER_VERTICAL,
        paddingLeftDp = 16.0f,
        paddingTopDp = 20.0f,
        paddingRightDp = 12.0f,
        paddingBottomDp = 12.0f,
        idStr = "ChoiceTemplateCreateCardTitle-$cardPosition",
        spanSize = spanSize
    )
    
    // 卡片列表
    carouselNoSnapWrapBuilder {
        id("ChoiceTemplateCreateCardList-$cardPosition")
        padding(Carousel.Padding.dp(16, 0, 6, 8, 0))
        hasFixedSize(true)
        initialPrefetchItemCount(3)
        onVisibilityStateChanged { _, _, visibilityState ->
            if (visibilityState == VisibilityState.VISIBLE) {
                listener.onCardShow(cardPosition, card)
            }
        }
        spanSizeOverride { _, _, _ ->
            spanSize
        }
        games.forEachIndexed { position, game ->
            add(
                ChoiceTemplateCreateCardItem(
                    game,
                    position,
                    card,
                    cardPosition,
                    spanSize,
                    listener
                ).id("ChoiceTemplateCreateCardGame-$cardPosition-$position-${game.code}")
            )
        }
    }
}

/**
 * 模板创建卡片Item
 */
data class ChoiceTemplateCreateCardItem(
    val item: ChoiceGameInfo,
    val position: Int,
    val card: ChoiceCardInfo,
    val cardPosition: Int,
    val spanSize: Int,
    val listener: IChoiceListener
) : ViewBindingItemModel<AdapterChoiceCardItemTemplateCreateBinding>(
    R.layout.adapter_choice_card_item_template_create,
    AdapterChoiceCardItemTemplateCreateBinding::bind
) {

    override fun AdapterChoiceCardItemTemplateCreateBinding.onBind() {
        // 设置卡片间距
        root.setMargin(right = dp(10))
        
        // 设置卡片宽度
        setupCardWidth()
        
        // 设置模板封面图片
        setupCoverImage()
        
        // 设置Template标签和星级评分
        setupTemplateLabel()
        
        // 设置点赞数
        setupLikeCount()
        
        // 设置游戏名称
        setupGameName()
        
        // 设置游戏标签
        setupGameTags()
        
        // 设置点击事件
        setupClickEvents()
    }

    private fun AdapterChoiceCardItemTemplateCreateBinding.setupCardWidth() {
        val layoutParams = root.layoutParams
        layoutParams.width = dp(160) // 比普通小卡片稍宽一些
        root.layoutParams = layoutParams
    }

    private fun AdapterChoiceCardItemTemplateCreateBinding.setupCoverImage() {
        listener.getGlideOrNull()?.run {
            load(item.imageUrl ?: item.iconUrl)
                .placeholder(R.drawable.placeholder_corner_12)
                .centerCrop()
                .into(ivTemplateCover)
        }
    }

    private fun AdapterChoiceCardItemTemplateCreateBinding.setupTemplateLabel() {
        // Template标签默认显示
        tvTemplateLabel.text = "Template"
        
        // 设置星级评分（这里简化为固定2星，实际项目中可以根据数据动态设置）
        setupStarRating(2)
    }

    private fun AdapterChoiceCardItemTemplateCreateBinding.setupStarRating(rating: Int) {
        // 根据评分显示星星
        ivStar1.visibility = if (rating >= 1) View.VISIBLE else View.GONE
        ivStar2.visibility = if (rating >= 2) View.VISIBLE else View.GONE
    }

    private fun AdapterChoiceCardItemTemplateCreateBinding.setupLikeCount() {
        val likeCount = item.localLikeCount ?: item.likeCount ?: 0
        likeCountView.setCount(likeCount)
    }

    private fun AdapterChoiceCardItemTemplateCreateBinding.setupGameName() {
        tvTemplateName.text = item.displayName?.trim() ?: ""
    }

    private fun AdapterChoiceCardItemTemplateCreateBinding.setupGameTags() {
        val tags = item.tagList
        if (tags.isNullOrEmpty()) {
            llGameTags.gone()
        } else {
            llGameTags.visible()
            
            // 设置第一个标签
            if (tags.isNotEmpty()) {
                tvTag1.visible()
                tvTag1.text = tags[0]
            } else {
                tvTag1.gone()
            }
            
            // 设置第二个标签
            if (tags.size > 1) {
                tvTag2.visible()
                tvTag2.text = tags[1]
            } else {
                tvTag2.gone()
            }
        }
    }

    private fun AdapterChoiceCardItemTemplateCreateBinding.setupClickEvents() {
        // 整个卡片点击事件
        root.setOnAntiViolenceClickListener {
            listener.onItemClick(cardPosition, card, position, item, false)
        }
        
        // Create按钮点击事件
        btnCreate.setOnAntiViolenceClickListener {
            listener.onCreateClick(cardPosition, card, position, item)
        }
        
        // 点赞按钮点击事件
        likeCountView.setOnClickListener {
            listener.onLikeClick(cardPosition, card, position, item)
        }
    }

    override fun AdapterChoiceCardItemTemplateCreateBinding.onUnbind() {
        root.setOnClickListener(null)
        btnCreate.setOnClickListener(null)
        likeCountView.setOnClickListener(null)
    }
}
