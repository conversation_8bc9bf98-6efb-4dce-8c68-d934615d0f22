<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginEnd="@dimen/dp_12">

    <!-- 主头像/Add Friend背景 -->
    <FrameLayout
        android:id="@+id/fl_avatar_container"
        android:layout_width="68dp"
        android:layout_height="@dimen/dp_74"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 头像图片 -->
        <ImageView
            android:id="@+id/iv_avatar"
            android:layout_width="@dimen/dp_58"
            android:layout_height="@dimen/dp_58"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_12"
            android:padding="@dimen/dp_3"
            android:scaleType="centerCrop"
            tools:src="@drawable/bg_friend_avatar_yellow" />

        <!-- Add Friend的加号图标(底部中心位置) -->
        <ImageView
            android:id="@+id/iv_add_icon"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="54dp"
            android:src="@drawable/ic_add_friend_plus"
            android:visibility="gone"
            tools:visibility="visible" />

    </FrameLayout>

    <!-- 状态标签 -->
    <LinearLayout
        android:id="@+id/ll_status"
        android:layout_width="wrap_content"
        android:paddingHorizontal="@dimen/dp_4"
        android:layout_height="@dimen/dp_14"
        android:layout_marginTop="@dimen/dp_56"
        android:background="@drawable/bg_status_online"
        android:gravity="center"
        android:orientation="horizontal"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/fl_avatar_container"
        app:layout_constraintStart_toStartOf="@id/fl_avatar_container"
        app:layout_constraintTop_toTopOf="@id/fl_avatar_container"
        tools:visibility="visible">

        <!-- 状态图标 -->
        <ImageView
            android:id="@+id/iv_status_icon_new"
            android:layout_width="@dimen/dp_10"
            android:layout_height="@dimen/dp_10"
            android:src="@drawable/ic_friend_online" />

        <!-- 状态文字 -->
        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_status_text"
            style="@style/MetaTextView.S8"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Online"
            android:textColor="@color/white" />

    </LinearLayout>

    <!-- Add Friend文字背景 -->
    <View
        android:id="@+id/view_add_friend_text_bg"
        android:layout_width="48dp"
        android:layout_height="9dp"
        android:layout_marginTop="8dp"
        android:background="@drawable/bg_add_friend_text_gradient"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/tv_user_name"
        app:layout_constraintStart_toStartOf="@id/tv_user_name"
        app:layout_constraintTop_toTopOf="@id/tv_user_name"
        tools:visibility="visible" />

    <!-- 用户名 -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_user_name"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="@color/Gray_900"
        app:layout_constraintEnd_toEndOf="@id/fl_avatar_container"
        app:layout_constraintStart_toStartOf="@id/fl_avatar_container"
        app:layout_constraintTop_toBottomOf="@id/fl_avatar_container"
        tools:text="Add Friend" />

</androidx.constraintlayout.widget.ConstraintLayout>