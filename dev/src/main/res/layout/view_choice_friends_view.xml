<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <!-- 有数据时显示的布局 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_has_friends"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="visible">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_friends_title"
            style="@style/MetaTextView.S14.PoppinsSemiBold600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:layout_marginTop="@dimen/dp_20"
            android:text="@string/friends_title"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_friends_list"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_8"
            android:layout_marginBottom="@dimen/dp_8"
            android:orientation="horizontal"
            android:overScrollMode="never"
            android:scrollbars="none"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_friends_title"
            tools:itemCount="10"
            tools:listitem="@layout/adapter_item_friends" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 无数据时显示的空状态布局 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_empty_friends"
        android:layout_width="match_parent"
        android:layout_height="123dp"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_20"
        android:layout_marginBottom="@dimen/dp_8"
        android:background="@drawable/bg_friends_empty_state"
        android:visibility="gone">

        <!-- 提示文字 -->
        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_empty_message"
            style="@style/MetaTextView.S12.PoppinsMedium500"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="@dimen/dp_16"
            android:gravity="start"
            android:lineSpacingExtra="@dimen/dp_2"
            android:text="Make Gpark more fun with friends! Meet and make new buddies!"
            android:textColor="@color/Gray_900"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 头像组 -->
        <LinearLayout
            android:id="@+id/layout_avatar_group"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingTop="@dimen/dp_12"
            android:paddingBottom="@dimen/dp_8"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_empty_message"
            app:layout_constraintVertical_bias="1.0">

            <!-- 黄色头像 -->
            <FrameLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">

                <ImageView
                    android:id="@+id/iv_avatar_1"
                    android:layout_width="52dp"
                    android:layout_height="52dp"
                    android:layout_gravity="center_horizontal|top"
                    android:scaleType="centerCrop"
                    android:src="@drawable/bg_friend_avatar_yellow" />

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="42dp"
                    android:src="@drawable/ic_add_friend_plus" />
            </FrameLayout>

            <!-- 蓝色头像 -->
            <FrameLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">

                <ImageView
                    android:id="@+id/iv_avatar_2"
                    android:layout_width="52dp"
                    android:layout_height="52dp"
                    android:layout_gravity="center_horizontal|top"
                    android:scaleType="centerCrop"
                    android:src="@drawable/bg_friend_avatar_blue" />

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="42dp"
                    android:src="@drawable/ic_add_friend_plus" />
            </FrameLayout>

            <!-- 紫色头像 -->
            <FrameLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">

                <ImageView
                    android:id="@+id/iv_avatar_3"
                    android:layout_width="52dp"
                    android:layout_height="52dp"
                    android:layout_gravity="center_horizontal|top"
                    android:scaleType="centerCrop"
                    android:src="@drawable/bg_friend_avatar_purple" />

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="42dp"
                    android:src="@drawable/ic_add_friend_plus" />
            </FrameLayout>

            <!-- 粉色头像 -->
            <FrameLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">

                <ImageView
                    android:id="@+id/iv_avatar_4"
                    android:layout_width="52dp"
                    android:layout_height="52dp"
                    android:layout_gravity="center_horizontal|top"
                    android:scaleType="centerCrop"
                    android:src="@drawable/bg_friend_avatar_pink" />

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="42dp"
                    android:src="@drawable/ic_add_friend_plus" />
            </FrameLayout>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>