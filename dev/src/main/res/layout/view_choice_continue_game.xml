<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/dp_16"
    android:layout_marginTop="@dimen/dp_10"
    android:background="@drawable/bg_light_yellow_corner_12">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_played_title"
        style="@style/MetaTextView.S12"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_yellow_played_round_12"
        android:drawableLeft="@drawable/ic_my_played_clock"
        android:drawablePadding="@dimen/dp_4"
        android:paddingHorizontal="@dimen/dp_8"
        android:paddingVertical="@dimen/dp_4"
        android:text="@string/played_title"
        android:textColor="@color/color_78340F"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_played_manage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingVertical="@dimen/dp_4"
        android:paddingRight="@dimen/dp_8"
        android:src="@drawable/ic_game_detail_common_operation_arrow"
        app:layout_constraintBottom_toBottomOf="@id/tv_played_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_played_title" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_played_manage"
        style="@style/MetaTextView.S12.PoppinsRegular400.CenterVertical"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingVertical="@dimen/dp_5"
        android:paddingLeft="@dimen/dp_16"
        android:text="@string/continue_gamedelete_manager"
        android:textColor="@color/Gray_800"
        app:layout_constraintBottom_toBottomOf="@id/tv_played_title"
        app:layout_constraintEnd_toStartOf="@id/iv_played_manage"
        app:layout_constraintTop_toTopOf="@id/tv_played_title" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_recently_played"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_10"
        android:layout_marginTop="@dimen/dp_8"
        android:orientation="horizontal"
        android:overScrollMode="never"
        android:scrollbars="none"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_played_title"
        tools:listitem="@layout/adapter_played" />

    <View
        android:id="@+id/vContinueSpace"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_8"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/rv_recently_played" />
    <!-- 首页下拉刷新无法触发: 当此Item的所有View都被隐藏后, 即便 recyclerView 滑动到最顶部 recyclerView.computeVerticalScrollOffset() 的值也不会为 0
         进而导致 recyclerView.canScrollVertically 为 true, SimpleSwipeRefreshLayout 的下拉刷新就不会触发
         当此Item存在一个可见的View时, recyclerView 滑动到最顶部 recyclerView.computeVerticalScrollOffset() 的值为 0,也能触发下拉刷新
         具体原因要看 EpoxyRecyclerView 的代码才行, 这里先暂时这样处理 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0.1dp"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>