<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/templateCreateCardRoot"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <!-- 主图片 -->
    <com.socialplay.gpark.ui.view.RoundImageView
        android:id="@+id/iv_template_cover"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:scaleType="centerCrop"
        app:bottomLeftRadius="@dimen/dp_12"
        app:bottomRightRadius="@dimen/dp_12"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:topLeftRadius="@dimen/dp_12"
        app:topRightRadius="@dimen/dp_12"
        tools:src="@color/default_shadow_color" />

    <!-- Template标签背景 -->
    <LinearLayout
        android:id="@+id/ll_template_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginTop="@dimen/dp_8"
        android:background="@drawable/bg_black_80_s8"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp_6"
        android:paddingVertical="@dimen/dp_2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- Template文字 -->
        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_template_label"
            style="@style/MetaTextView.S9.PoppinsMedium500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Template"
            android:textColor="@color/white" />

        <!-- 星级评分 -->
        <LinearLayout
            android:id="@+id/ll_rating"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_4"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_star1"
                android:layout_width="@dimen/dp_8"
                android:layout_height="@dimen/dp_8"
                android:src="@drawable/ic_star_filled"
                android:tint="@color/color_FFEF30"
                tools:ignore="ContentDescription" />

            <ImageView
                android:id="@+id/iv_star2"
                android:layout_width="@dimen/dp_8"
                android:layout_height="@dimen/dp_8"
                android:layout_marginStart="@dimen/dp_1"
                android:src="@drawable/ic_star_filled"
                android:tint="@color/color_FFEF30"
                tools:ignore="ContentDescription" />

        </LinearLayout>
    </LinearLayout>

    <!-- 点赞数 -->
    <com.socialplay.gpark.ui.view.LikeCountView
        android:id="@+id/like_count_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_8"
        android:layout_marginBottom="@dimen/dp_8"
        app:layout_constraintBottom_toBottomOf="@id/iv_template_cover"
        app:layout_constraintEnd_toEndOf="@id/iv_template_cover"
        app:likeBackground="@drawable/bg_black_80_s8"
        app:likeContentPaddingHorizontal="@dimen/dp_6"
        app:likeContentPaddingVertical="@dimen/dp_2"
        app:likeCount="22.2k"
        app:likeCountTextColor="@color/white"
        app:likeCountTextStyle="@style/MetaTextView.S9.PoppinsMedium500"
        app:likeIcon="@drawable/icon_item_like"
        app:likeIconPadding="@dimen/dp_2" />

    <!-- 游戏名称 -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_template_name"
        style="@style/MetaTextView.S14.PoppinsMedium500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:layout_marginTop="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_4"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/textColorPrimary"
        app:layout_constraintEnd_toStartOf="@id/btn_create"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_template_cover"
        tools:text="Enchilada Casserole" />

    <!-- Create按钮 -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/btn_create"
        style="@style/MetaTextView.S12.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_4"
        android:background="@drawable/bg_ffef30_round_6"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dp_12"
        android:paddingVertical="@dimen/dp_4"
        android:text="Create"
        android:textColor="@color/textColorPrimary"
        app:layout_constraintBottom_toBottomOf="@id/tv_template_name"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_template_name" />

    <!-- 游戏类型标签 -->
    <LinearLayout
        android:id="@+id/ll_game_tags"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:layout_marginTop="@dimen/dp_4"
        android:layout_marginEnd="@dimen/dp_4"
        android:layout_marginBottom="@dimen/dp_8"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_template_name">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_tag1"
            style="@style/MetaTextView.S10.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_f0f0f0_corner_8"
            android:paddingHorizontal="@dimen/dp_6"
            android:paddingVertical="@dimen/dp_2"
            android:textColor="@color/color_666666"
            android:visibility="gone"
            tools:text="Playful"
            tools:visibility="visible" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_tag2"
            style="@style/MetaTextView.S10.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_4"
            android:background="@drawable/bg_f0f0f0_corner_8"
            android:paddingHorizontal="@dimen/dp_6"
            android:paddingVertical="@dimen/dp_2"
            android:textColor="@color/color_666666"
            android:visibility="gone"
            tools:text="RPG"
            tools:visibility="visible" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
