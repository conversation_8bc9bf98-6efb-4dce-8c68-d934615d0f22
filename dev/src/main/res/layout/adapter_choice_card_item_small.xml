<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/smallCardRoot"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.socialplay.gpark.ui.view.RoundImageView
        android:id="@+id/iv_game_icon"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:scaleType="centerCrop"
        app:bottomLeftRadius="@dimen/dp_0"
        app:bottomRightRadius="@dimen/dp_0"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:topLeftRadius="@dimen/dp_12"
        app:topRightRadius="@dimen/dp_12"
        tools:src="@color/default_shadow_color" />

    <com.socialplay.gpark.ui.view.RoundImageView
        android:id="@+id/iv_name_bg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_34"
        android:src="@color/color_A868FF"
        app:bottomLeftRadius="@dimen/dp_12"
        app:bottomRightRadius="@dimen/dp_12"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_game_icon"
        app:topLeftRadius="@dimen/dp_0"
        app:topRightRadius="@dimen/dp_0"
        tools:src="@color/color_A868FF" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_game_title"
        style="@style/MetaTextView.S13.PoppinsRegular400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_6"
        android:ellipsize="end"
        android:gravity="center"
        android:lines="1"
        android:maxLines="1"
        android:minLines="1"
        android:paddingHorizontal="@dimen/dp_3"
        android:textColor="@color/white"
        app:layout_constraintTop_toBottomOf="@+id/iv_game_icon"
        tools:text="斗罗大陆里边有个兔子" />


    <ImageView
        android:id="@+id/iv_like_bg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/iv_game_icon"
        app:layout_constraintLeft_toLeftOf="@id/iv_game_icon"
        app:layout_constraintRight_toRightOf="@id/iv_name_bg"
        app:layout_constraintTop_toTopOf="@id/iv_game_icon" />

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/bg_item_like"
        app:layout_constraintBottom_toBottomOf="@id/ll_like"
        app:layout_constraintLeft_toLeftOf="@id/ll_like"
        app:layout_constraintRight_toRightOf="@id/ll_like"
        app:layout_constraintTop_toTopOf="@id/ll_like" />

    <!--    <View-->
    <!--        android:layout_width="0dp"-->
    <!--        android:layout_height="0dp"-->
    <!--        android:layout_marginLeft="-6dp"-->
    <!--        android:layout_marginRight="-6dp"-->
    <!--        android:background="@drawable/icon_item_like_bg2"-->
    <!--        app:layout_constraintBottom_toBottomOf="@id/tv_zan"-->
    <!--        app:layout_constraintLeft_toLeftOf="@id/tv_zan"-->
    <!--        app:layout_constraintRight_toRightOf="@id/tv_zan"-->
    <!--        app:layout_constraintTop_toTopOf="@id/tv_zan" />-->

    <!--    <com.socialplay.gpark.ui.view.MetaTextView-->
    <!--        android:id="@+id/tv_zan"-->
    <!--        style="@style/MetaTextView.S9.PoppinsMedium500"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layout_marginLeft="@dimen/dp_16"-->
    <!--        android:layout_marginBottom="@dimen/dp_8"-->
    <!--        android:drawableStart="@drawable/icon_item_like"-->
    <!--        android:drawablePadding="@dimen/dp_2"-->
    <!--        android:drawableTint="@color/white"-->
    <!--        android:gravity="center"-->
    <!--        android:minHeight="@dimen/dp_18"-->
    <!--        android:textColor="@color/white"-->
    <!--        app:layout_constraintBottom_toTopOf="@id/iv_name_bg"-->
    <!--        app:layout_constraintStart_toStartOf="parent"-->
    <!--        tools:text="a321515135k" />-->


    <LinearLayout
        android:id="@+id/ll_like"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_8"
        android:layout_marginBottom="@dimen/dp_8"
        android:background="@drawable/icon_item_like_bg2"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp_6"
        android:paddingVertical="@dimen/dp_2"
        app:layout_constraintBottom_toTopOf="@id/iv_name_bg"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/icon_item_like" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_zan"
            style="@style/MetaTextView.S9.PoppinsMedium500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_2"
            android:gravity="center"
            android:textColor="@color/white"
            tools:text="a321515135k" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>