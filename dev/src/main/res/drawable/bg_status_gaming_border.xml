<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 渐变描边层（外圈） -->
    <item>
        <shape android:shape="oval">
            <gradient
                android:angle="180"
                android:endColor="@color/color_CA9FFF"
                android:startColor="@color/color_898BFE"
                android:type="linear"
                android:useLevel="false" />
        </shape>
    </item>
    <!-- 内层白色圆（内圈） -->
    <item
        android:bottom="2dp"
        android:left="2dp"
        android:right="2dp"
        android:top="2dp">
        <shape android:shape="oval">
            <solid android:color="#FFFFFF" />
        </shape>
    </item>
</layer-list>