# Template Create Card 使用示例

## 概述

新增的 `TEMPLATE_CREATE` 卡片类型用于显示模板类型的游戏卡片，具有以下特征：
- 左上角显示"Template"标签和星级评分
- 右下角显示黄色的"Create"按钮
- 底部显示游戏名称和类型标签
- 支持点赞功能

## 使用方法

### 1. 后端数据配置

在后端返回的 `ChoiceCardInfo` 中设置卡片类型为 `TEMPLATE_CREATE`：

```json
{
  "cardType": 1029,
  "cardName": "Template Games",
  "gameList": [
    {
      "code": "template_game_1",
      "displayName": "Enchilada Casserole",
      "imageUrl": "https://example.com/template_cover.jpg",
      "likeCount": 22200,
      "tagList": ["Playful", "RPG"]
    }
  ]
}
```

### 2. 前端实现

#### 在Fragment或Activity中实现IChoiceListener接口：

```kotlin
class RecommendFragment : Fragment(), IChoiceListener {
    
    // 实现Create按钮点击事件
    override fun onCreateClick(cardPosition: Int, card: ChoiceCardInfo, position: Int, item: ChoiceGameInfo) {
        // 处理模板创建逻辑
        navigateToTemplateEditor(item)
    }
    
    // 实现点赞按钮点击事件
    override fun onLikeClick(cardPosition: Int, card: ChoiceCardInfo, position: Int, item: ChoiceGameInfo) {
        // 处理点赞逻辑
        handleLikeAction(item)
    }
    
    private fun navigateToTemplateEditor(game: ChoiceGameInfo) {
        // 跳转到模板编辑器
        val intent = Intent(context, TemplateEditorActivity::class.java)
        intent.putExtra("template_id", game.code)
        intent.putExtra("template_name", game.displayName)
        startActivity(intent)
    }
    
    private fun handleLikeAction(game: ChoiceGameInfo) {
        // 发送点赞请求
        viewModel.likeTemplate(game.code) { success ->
            if (success) {
                // 更新UI或显示成功提示
                showToast("点赞成功")
            }
        }
    }
}
```

### 3. 卡片特性

#### 模板标签和星级评分
- 固定显示"Template"文字
- 星级评分目前为固定2星（可根据需求扩展为动态评分）

#### Create按钮
- 黄色背景，圆角设计
- 点击后触发 `onCreateClick` 回调
- 通常用于跳转到模板编辑器或创建页面

#### 游戏标签
- 最多显示2个标签
- 标签来源于 `ChoiceGameInfo.tagList`
- 灰色背景，圆角设计

#### 点赞功能
- 使用自定义的 `LikeCountView` 组件
- 支持大数字格式化（K、M后缀）
- 点击后触发 `onLikeClick` 回调

### 4. 样式自定义

如需自定义样式，可以修改以下资源文件：

#### 布局文件
- `adapter_choice_card_item_template_create.xml`

#### 颜色资源
- Template标签背景：`bg_black_80_s8`
- Create按钮背景：`bg_ffef30_round_6`
- 星星颜色：`color_FFEF30`
- 标签文字颜色：`color_666666`

#### 尺寸资源
- 卡片宽度：160dp（在代码中设置）
- 星星大小：8dp x 8dp
- 各种间距：使用项目标准尺寸

### 5. 数据绑定

卡片会自动绑定以下数据：
- `item.imageUrl` 或 `item.iconUrl` → 封面图片
- `item.displayName` → 游戏名称
- `item.localLikeCount` 或 `item.likeCount` → 点赞数
- `item.tagList` → 游戏标签（最多2个）

### 6. 注意事项

1. **图片加载**：使用项目的Glide实例进行图片加载
2. **点击防抖**：使用 `setOnAntiViolenceClickListener` 防止重复点击
3. **内存管理**：在 `onUnbind` 中清理点击监听器
4. **标签显示**：如果没有标签数据，标签容器会自动隐藏
5. **星级评分**：目前为固定显示，如需动态评分可扩展 `setupStarRating` 方法

## 效果预览

卡片最终效果类似于：
```
┌─────────────────────┐
│ Template ⭐⭐    👍22.2k│
│                     │
│    [游戏封面图片]      │
│                     │
│                     │
└─────────────────────┘
Enchilada Casserole  [Create]
[Playful] [RPG]
```

## 扩展建议

1. **动态星级评分**：根据模板质量或用户评分动态显示星星数量
2. **更多标签支持**：支持显示更多标签或横向滚动
3. **动画效果**：为Create按钮和点赞添加动画效果
4. **主题适配**：支持深色模式或其他主题
